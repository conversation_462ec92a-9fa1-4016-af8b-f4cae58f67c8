import json
import requests
from django.conf import settings



ENABLED = settings.MARKETING_TRIGGERS_ENABLED
BASE_FRONTEND_URL = settings.BASE_FRONTEND_URL
ACCESS_TOKEN = settings.HUBSPOT_ACCESS_TOKEN
NEWSLETTER_MEMBERS = settings.HUBSPOT_NEWSLETTER_GENERAL_LIST_ID
FREE_USERS = settings.HUBSPOT_FREE_MEMBERS_LIST_ID
GLOBAL_USERS = settings.HUBSPOT_NEWSLETTER_GLOBAL_USERS_LIST_ID
COMMUNITY_MONTHLY = settings.HUBSPOT_COMMUNITY_MONTHLY_LIST_ID
COMMUNITY_YEARLY = settings.HUBSPOT_COMMUNITY_YEARLY_LIST_ID
THRIVE_MONTHLY = settings.HUBSPOT_THRIVE_MONTHLY_LIST_ID
THRIVE_YEARLY = settings.HUBSPOT_THRIVE_YEARLY_LIST_ID

# Digital Ocean Functions configuration
DO_FUNCTIONS_URL = f"https://{settings.DIGITAL_OCEAN_FUNCTIONS_NAMESPACE}.{settings.DIGITAL_OCEAN_FUNCTIONS_REGION}.doserverless.co/api/v1/web/fn-{settings.DIGITAL_OCEAN_FUNCTIONS_NAMESPACE}/hubspot/handler"
DO_FUNCTIONS_AUTH_TOKEN = settings.DIGITAL_OCEAN_FUNCTIONS_AUTH_TOKEN

# Callback URL for async operations
CALLBACK_URL = f"{settings.BASE_BACKEND_URL}/api/dispatcher/hubspot-callback/"

def call_do_function(action, payload, callback_token=None):
    """
    Call Digital Ocean Function with the given action and payload
    """
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {DO_FUNCTIONS_AUTH_TOKEN}"
    }

    # Add action to payload
    payload["action"] = action

    # Add callback information if provided
    if callback_token:
        payload["callback_url"] = CALLBACK_URL
        payload["callback_token"] = callback_token

    try:
        response = requests.post(DO_FUNCTIONS_URL, json=payload, headers=headers)
        return response.json()
    except Exception as e:
        error_message = f"Exception when calling DO Function {action}: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
        return {"status": "error", "message": str(e)}

def create_hubspot_contact(simple_public_object_input_for_create, is_guest=False):
    """
    Create a Hubspot contact using Digital Ocean Function
    """
    payload = {
        "data": simple_public_object_input_for_create,
        "is_guest": is_guest
    }
    return call_do_function("create_contact", payload)

def update_hubspot_contact(contact_id, simple_public_object_input):
    """
    Update a Hubspot contact using Digital Ocean Function
    """
    payload = {
        "contact_id": contact_id,
        "data": simple_public_object_input
    }
    return call_do_function("update_contact", payload)

def subscribe_user_to_list(contact_id, mailing_list_id):
    """
    Subscribe a user to a Hubspot list using Digital Ocean Function
    """
    payload = {
        "contact_id": contact_id,
        "mailing_list_id": mailing_list_id
    }
    return call_do_function("subscribe_to_list", payload)

def unsubscribe_user_from_list(contact_id, mailing_list_id):
    """
    Unsubscribe a user from a Hubspot list using Digital Ocean Function
    """
    payload = {
        "contact_id": contact_id,
        "mailing_list_id": mailing_list_id
    }
    return call_do_function("unsubscribe_from_list", payload)

def get_contact_by_id(contact_id):
    """
    Get a Hubspot contact by ID (not using DO Function for now)
    """
    headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": f"Bearer {ACCESS_TOKEN}"
    }
    url = f"https://api.hubapi.com/contacts/v1/contact/vid/{contact_id}/profile/"
    params = {'showListMemberships': True}

    try:
        response = requests.get(url, params=params, headers=headers)
        return response.json()
    except Exception as e:
        error_message = f"Exception when getting contact details {contact_id}: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
    return {}

def get_contact_by_email(email):
    """
    Get a Hubspot contact by email (not using DO Function for now)
    """
    headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": f"Bearer {ACCESS_TOKEN}"
    }
    url = f"https://api.hubapi.com/contacts/v1/contact/email/{email}/profile"
    params = {'showListMemberships': True}

    try:
        response = requests.get(url, params=params, headers=headers)
        return response.json()
    except Exception as e:
        error_message = f"Exception when getting contact details {email}: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
    return {}

def get_contact_subscription_preferences(email):
    """
    Get contact subscription preferences (not using DO Function for now)
    """
    # This is a synchronous operation that returns data, so we'll keep it direct
    # If it becomes a bottleneck, we can consider caching or other strategies
    try:
        from hubspot import HubSpot
        hubspot_client = HubSpot(access_token=ACCESS_TOKEN)
        return hubspot_client.communication_preferences.status_api.get_email_status(email_address=email)
    except Exception as e:
        error_message = f"Exception when getting subscription preferences: {email}: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)
    return {}

def update_contact_subscription_preferences(email, subscription_id):
    """
    Update contact subscription preferences using Digital Ocean Function
    """
    payload = {
        "email": email,
        "subscription_id": subscription_id
    }
    return call_do_function("update_subscription_preferences", payload)

# Async versions with callbacks

def create_hubspot_contact_async(simple_public_object_input_for_create, callback_token, is_guest=False):
    """
    Create a Hubspot contact using Digital Ocean Function with callback
    """
    payload = {
        "data": simple_public_object_input_for_create,
        "is_guest": is_guest
    }
    return call_do_function("create_contact", payload, callback_token)

def update_hubspot_contact_async(contact_id, simple_public_object_input, callback_token):
    """
    Update a Hubspot contact using Digital Ocean Function with callback
    """
    payload = {
        "contact_id": contact_id,
        "data": simple_public_object_input
    }
    return call_do_function("update_contact", payload, callback_token)

def subscribe_user_to_list_async(contact_id, mailing_list_id, callback_token=None):
    """
    Subscribe a user to a Hubspot list using Digital Ocean Function with optional callback
    """
    payload = {
        "contact_id": contact_id,
        "mailing_list_id": mailing_list_id
    }
    return call_do_function("subscribe_to_list", payload, callback_token)

def unsubscribe_user_from_list_async(contact_id, mailing_list_id, callback_token=None):
    """
    Unsubscribe a user from a Hubspot list using Digital Ocean Function with optional callback
    """
    payload = {
        "contact_id": contact_id,
        "mailing_list_id": mailing_list_id
    }
    return call_do_function("unsubscribe_from_list", payload, callback_token)
