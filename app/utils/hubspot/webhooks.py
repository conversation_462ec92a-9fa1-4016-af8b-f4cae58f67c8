import uuid
from django.core.cache import cache
from django.utils import timezone
from config import settings


def generate_callback_token():
    """Generate a unique token for callback identification."""
    return str(uuid.uuid4())


def store_callback_expectation(token, operation_type, user_id=None, email=None, extra_data=None):
    """Store information about an expected callback."""
    callback_data = {
        'operation_type': operation_type,
        'user_id': user_id,
        'email': email,
        'extra_data': extra_data or {},
        'timestamp': str(timezone.now())
    }

    # Store for 10 minutes (should be enough for HubSpot operations)
    cache.set(f"hubspot_callback_{token}", callback_data, timeout=600)
    return token


def handle_hubspot_callback(data):
    """Handle incoming HubSpot callback with results."""
    token = data.get('callback_token')
    if not token:
        raise ValueError("Missing callback_token in HubSpot callback")

    # Retrieve the stored callback expectation
    callback_key = f"hubspot_callback_{token}"
    callback_data = cache.get(callback_key)

    if not callback_data:
        raise ValueError(f"No callback expectation found for token: {token}")

    # Remove the callback expectation (one-time use)
    cache.delete(callback_key)

    operation_type = callback_data['operation_type']

    if operation_type == 'create_contact':
        _handle_create_contact_callback(data, callback_data)
    elif operation_type == 'update_contact':
        _handle_update_contact_callback(data, callback_data)
    elif operation_type == 'subscribe_to_list':
        _handle_subscribe_callback(data, callback_data)
    elif operation_type == 'unsubscribe_from_list':
        _handle_unsubscribe_callback(data, callback_data)
    else:
        print(f"Unknown HubSpot callback operation: {operation_type}")


def _handle_create_contact_callback(data, callback_data):
    """Handle create_contact callback - update user with HubSpot ID."""
    from django.contrib.auth import get_user_model
    User = get_user_model()

    hubspot_id = data.get('id')
    status = data.get('status')

    if status == 'success' and hubspot_id:
        user_id = callback_data.get('user_id')
        if user_id:
            try:
                user = User.objects.get(id=user_id)
                user.hubspot_id = hubspot_id
                user.save()
                print(f"Updated user {user.email} with HubSpot ID: {hubspot_id}")

                # If this was for a guest contact, handle subscription
                extra_data = callback_data.get('extra_data', {})
                if extra_data.get('is_guest') and extra_data.get('mailing_list_id'):
                    from .client import subscribe_user_to_list_async
                    subscribe_user_to_list_async(hubspot_id, extra_data['mailing_list_id'])

            except User.DoesNotExist:
                print(f"User with ID {user_id} not found for HubSpot callback")
    else:
        print(f"HubSpot create_contact failed: {data}")


def _handle_update_contact_callback(data, callback_data):
    """Handle update_contact callback - mostly for logging."""
    status = data.get('status')
    user_id = callback_data.get('user_id')

    if status == 'success':
        print(f"HubSpot contact update successful for user {user_id}")
    else:
        print(f"HubSpot contact update failed for user {user_id}: {data}")


def _handle_subscribe_callback(data, callback_data):
    """Handle subscription callback - mostly for logging."""
    status = data.get('status')
    user_id = callback_data.get('user_id')

    if status == 'success':
        print(f"HubSpot subscription successful for user {user_id}")
    else:
        print(f"HubSpot subscription failed for user {user_id}: {data}")


def _handle_unsubscribe_callback(data, callback_data):
    """Handle unsubscription callback - mostly for logging."""
    status = data.get('status')
    user_id = callback_data.get('user_id')

    if status == 'success':
        print(f"HubSpot unsubscription successful for user {user_id}")
    else:
        print(f"HubSpot unsubscription failed for user {user_id}: {data}")