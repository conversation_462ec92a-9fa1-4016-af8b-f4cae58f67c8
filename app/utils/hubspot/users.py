from hubspot.crm.contacts import (
    SimplePublicObjectInput,
    SimplePublicObjectInputForCreate,
)
from hubspot.crm.contacts.exceptions import ApiException

from api.categories.models import categories

from config import settings

from .client import (
    create_hubspot_contact,
    create_hubspot_contact_async,
    update_hubspot_contact_async,
    subscribe_user_to_list_async,
    ENABLED,
    BASE_FRONTEND_URL,
    NEWSLETTER_MEMBERS,
    FREE_USERS,
    GLOBAL_USERS,
    get_contact_by_email,
    get_contact_by_id,
    get_contact_subscription_preferences,
    update_contact_subscription_preferences,
    subscribe_user_to_list,
    unsubscribe_user_from_list,
    update_hubspot_contact,
)
from .webhooks import generate_callback_token, store_callback_expectation



def get_email_active_subscription_preferences_ids(email):
    subscription_preferences = get_contact_subscription_preferences(email)
    subscription_ids = map(lambda sub: sub.id if sub.status == 'SUBSCRIBED' else None,
                           subscription_preferences.subscription_statuses)
    return list(filter(lambda sub_id: sub_id is not None, subscription_ids))


def update_email_subscription_preferences(active_subscription_ids, new_email):
    """Hubspot contact subscription preferences should be updated on email
    change."""
    for subscription_id in active_subscription_ids:
        r = update_contact_subscription_preferences(new_email, subscription_id)


def create_contact(user, is_guest=False, use_async=False):
    if ENABLED:
        data = {
            "email": user.email,
            "firstname": user.first_name,
            "lastname": user.last_name,
            "plan_id": user.plan_id,
            "account_verified": False,
        } if not is_guest else { "email": user }

        if use_async and not is_guest:
            # Use async for regular users where we need the HubSpot ID
            callback_token = generate_callback_token()
            store_callback_expectation(
                callback_token,
                'create_contact',
                user_id=user.id,
                email=user.email if is_guest else None
            )

            response = create_hubspot_contact_async(
                simple_public_object_input_for_create=SimplePublicObjectInputForCreate(
                    properties=data),
                callback_token=callback_token,
                is_guest=is_guest
            )
            print(f"hubspot.create_contact_async: {user.email} (callback: {callback_token})")
            return {"status": "async", "callback_token": callback_token}
        else:
            # Use synchronous for guests or when async is not needed
            try:
                response = create_hubspot_contact(
                    simple_public_object_input_for_create=SimplePublicObjectInputForCreate(
                        properties=data))
                if is_guest:
                    print(f"hubspot.create_contact: {user}[{response.get('id')}]")
                    return response
                user.hubspot_id = response.get('id')
                user.save()
                print(f"hubspot.create_contact: {user.email}[{response.get('id')}]")

                return response
            except ApiException as e:
                try:
                    # contact might already exist
                    if is_guest:
                        email = user
                        hubspot_contact = get_contact_by_email(email)
                        return {"id": hubspot_contact.get("vid")}
                    else:
                        hubspot_contact = get_contact_by_email(user.email)
                    user.hubspot_id = hubspot_contact.get("vid")
                    user.subscribed_to_newsletter = is_user_in_mailing_list(user)
                    user.save()
                    print(f"hubspot.create_contact (existing): {user.email}[{user.hubspot_id}]")
                    update_user_details(user)
                except Exception as exception:
                    error_message = f"Exception when creating contact: {user.email}: {e}"
                    print(error_message)
                    if settings.SENTRY_ENABLED:
                        from sentry_sdk import capture_message
                        capture_message(error_message)
                    return {}
    else:
        print("hubspot.create-contact: disabled")
    return {}



def subscribe_user(user, mailing_list_id=FREE_USERS):
    if ENABLED:
        response = subscribe_user_to_list(user.hubspot_id, mailing_list_id)
        print(f"hubspot.subscribe_user: {user.email}[{user.hubspot_id}] to {mailing_list_id}")
        return response
    else:
        print("hubspot.subscribe-user: disabled")
    return {}


def subscribe_new_user(user):
    if ENABLED:
        subscribe_user(user, FREE_USERS)
        subscribe_user(user, GLOBAL_USERS)
    else:
        print(f"hubspot.subscribe-new-user: {user.email} disabled")


def unsubscribe_user(user, mailing_list_id):
    try:
        if ENABLED:
            response = unsubscribe_user_from_list(user.hubspot_id, mailing_list_id)
            print(f"hubspot.unsubscribe_user: {user.email}[{user.hubspot_id}] from {mailing_list_id}")
            return response
        else:
            print(f"hubspot.unsubscribe-user: "
                  f"{user.email}[{user.hubspot_id}]:{mailing_list_id} disabled")
        return {}
    except Exception as e:
            error_message = f"hubspot.unsubscribe-user: " \
                            f"{user.email}[{user.hubspot_id}]:{mailing_list_id}: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)


def delete_user(email):
    try:
        if ENABLED:
            print('delete_user not implemented yet')
        else:
            print(f"hubspot.delete-user: {email} disabled")
    except Exception as e:
            error_message = f"Hubspot delete user {email}: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)


def update_all_user_details(user):
    try:
        data = {
            'name': user.full_name,
            'fields': {
                'activation_url': user.activation_token,
                'plan_id': user.plan_id,
            }
        }

        if ENABLED:
            print('update_all_user_details not implemented yet')
        else:
            print(f"hubspot.update-all-user-details: {user.email} disabled")
        return {}
    except Exception as e:
            error_message = f"Hubspot update user {user.email} info: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)


def update_user_details(user, custom=None, previous_email=False):
    try:
        if ENABLED:
            active_sub_ids = []
            if previous_email:
                update_account_verified(user, False)
                active_sub_ids = get_email_active_subscription_preferences_ids(
                    previous_email)

            data = {
                "email": user.email,
                "firstname": user.first_name,
                "lastname": user.last_name,
                "plan_id": user.plan_id,
                "member_location": ', '.join([user.profile.private_details.get('location'),
                                              user.profile.private_details.get('country')]),
                "member_dob": user.profile.private_details.get('birthdate'),
                "member_gender": user.profile.private_details.get('gender'),
            }
            if custom:
                data.update(custom)
            response = update_hubspot_contact(user.hubspot_id,
                                              simple_public_object_input=SimplePublicObjectInput(
                                                  properties=data))
            if active_sub_ids:
                update_email_subscription_preferences(
                    active_sub_ids, user.email)
            print(f"hubspot.update-user-details: {user.email}[{user.hubspot_id}]")
            return response
        else:
            print(f"hubspot.update-user-details: {user.email} disabled")
        return {}
    except ApiException as e:
            error_message = f"Hubspot update user {user.email} info: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)


def update_user_custom_property_url(user, custom_property_data):
    try:
        if ENABLED:
            response = update_hubspot_contact(
                user.hubspot_id,
                simple_public_object_input=SimplePublicObjectInput(
                    properties=custom_property_data))
            print(f"hubspot.update-user-custom-property: {user.email}[{user.hubspot_id}]: {list(custom_property_data.keys())}")
            return response
        else:
            print(f"hubspot.update-user-custom-property: {user.email} disabled")
        return {}
    except ApiException as e:
        error_message = f"Hubspot update user {user.email}[{user.hubspot_id}] custom property: {e}"
        print(error_message)
        if settings.SENTRY_ENABLED:
            from sentry_sdk import capture_message
            capture_message(error_message)


def update_password_reset_token(user):
    password_reset_url = f"{BASE_FRONTEND_URL}?reset_token={user.password_reset_token}"

    return update_user_custom_property_url(
        user, {"password_reset_url": password_reset_url})


def update_gift_coupon_code(user, coupon_code, giver=False):
    data = {"gift_coupon_code": coupon_code}
    if giver:
        data.update({"gift_giver": True})
    return update_user_custom_property_url(user, data)


def update_activation_token(user, activation_property="account_activation_url"):
    activation_url = f"{BASE_FRONTEND_URL}?activation_token={user.activation_token}"

    return update_user_custom_property_url(
        user, {activation_property: activation_url})


def update_account_verified(user, verified=True):
    return update_user_custom_property_url(
        user, {"account_verified": verified})


def update_subscription_cancellation_scheduled(user, canceled=True):
    return update_user_custom_property_url(
        user, {"membership_cancellation_scheduled": canceled})


def subscribe_email(email, mailing_list_id=NEWSLETTER_MEMBERS, use_async=False):
    if ENABLED:
        if use_async:
            # For async, create contact with callback that will handle subscription
            callback_token = generate_callback_token()
            store_callback_expectation(
                callback_token,
                'create_contact',
                email=email,
                extra_data={'is_guest': True, 'mailing_list_id': mailing_list_id}
            )

            contact_response = create_hubspot_contact_async(
                simple_public_object_input_for_create=SimplePublicObjectInputForCreate(
                    properties={"email": email}),
                callback_token=callback_token,
                is_guest=True
            )
            print(f"hubspot.subscribe-email-async: {email} to {mailing_list_id} (callback: {callback_token})")
            return {"status": "async", "callback_token": callback_token}
        else:
            # Synchronous version
            contact_response = create_contact(email, is_guest=True)
            try:
                hubspot_contact_id = contact_response.get('id')
            except AttributeError:
                hubspot_contact_id = contact_response.get("id")
            subscription_response = subscribe_user_to_list(
                hubspot_contact_id, mailing_list_id)
            print(f"hubspot.subscribe-email: {email} to {mailing_list_id}")
            return subscription_response
    else:
        print("hubspot.subscribe-email: disabled")
    return {}

def is_user_in_mailing_list(user, mailing_list_id=NEWSLETTER_MEMBERS):
    contact_details = get_contact_by_id(user.hubspot_id)

    return int(mailing_list_id) in map(
        lambda lists: lists.get('static-list-id'),
        contact_details.get('list-memberships', []))

def user_common_mailing_lists(user, mailing_lists_ids):
    contact_details = get_contact_by_id(user.hubspot_id)

    return set(mailing_lists_ids) & set(map(
        lambda lists: lists.get('static-list-id'),
        contact_details.get('list-memberships', [])))

def update_user_interests_from_mailing_lists(user):
    if ENABLED:
        try:
            categories_response = categories.search({})
            categories_list = categories_response.get('hits', [])
            category_dict = {}

            for entry in categories_list:
                category_dict[int(entry.get('hubspot_list_id'))] = {
                    'slug': entry.get('slug'),
                    'id': entry.get('id'),
                    'title': entry.get('title'),
                    'hubspot_list_id': entry.get('hubspot_list_id'),
                }
            common_mailing_lists = user_common_mailing_lists(
                user, category_dict.keys())

            user_interests = {}
            for mailing_list_id in common_mailing_lists:
                interest = category_dict.get(mailing_list_id)
                user_interests[interest.get('slug')] = interest
            user.profile.interests = user_interests
            user.profile.save()
        except Exception as e:
            error_message = f"Hubspot.subscribe-interests: {user.email}: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
    else:
        print(f"hubspot.subscribe-user-to-interests-lists: {user.email} disabled")

def map_user_billing_address(billing_address_data):
    return {
        'address_line_1': billing_address_data.get('line1'),
        'address_line_2': billing_address_data.get('line2'),
        'state': billing_address_data.get('state'),
        'city': billing_address_data.get('city'),
        'country': billing_address_data.get('country'),
        'zip': billing_address_data.get('zip'),
    }

def map_user_latest_purchase(object_data, field='latest_course_purchase'):
    return {
        field: object_data.get('title'),
    }


def update_abandoned_purchase_cart_details(user, product=None, coupon_code=None):
    if not product:
        data = {
            "abandoned_purchase": False,
            "abandoned_purchase_title": "",
            "abandoned_purchase_url": "",
            "abandoned_purchase_coupon_code": "",
        }
    else:
        url_path = f"{product.get('content_type')}/{product.get('slug')}"
        data = {
            "abandoned_purchase": True,
            "abandoned_purchase_title": product.get('title'),
            "abandoned_purchase_url": f"{BASE_FRONTEND_URL}{url_path}",
            "abandoned_purchase_coupon_code": coupon_code,
        }

    return update_user_custom_property_url(user, data)


def update_abandoned_subscription_cart_details(user, plan_id=None, coupon_code=None, cancelled=False):
    plan_id = plan_id or user.plan_id
    if plan_id in settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS:
        # yearly abandoned cart list
        abandoned_membership_list = settings.HUBSPOT_ABANDONED_CART_YEARLY_LIST_ID
    else:
        # quarterly abandoned cart list
        abandoned_membership_list = settings.HUBSPOT_ABANDONED_CART_QUARTERLY_LIST_ID

    data = {}
    hubspot_property = "abandoned_membership_purchase_coupon_code"
    if cancelled:
        hubspot_property = "cancelled_membership_coupon_code"
    if not coupon_code:
        data.update({hubspot_property: ""})
        # remove user from abandoned card membership list
        unsubscribe_user(user, abandoned_membership_list)
    else:
        data.update({hubspot_property: coupon_code})
        if not cancelled:
            # add user to abandoned card membership list
            subscribe_user(user, abandoned_membership_list)

    return update_user_custom_property_url(user, data)
