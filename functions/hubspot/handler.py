from hubspot import HubSpot
from hubspot.communication_preferences import PublicUpdateSubscriptionStatusRequest
import sentry_sdk

import json
import os
import requests



# Initialize Sentry if enabled
SENTRY_ENABLED = os.environ.get('ENABLE_SENTRY', 'False') == 'True'
if SENTRY_ENABLED:
    sentry_sdk.init(
        dsn=os.environ.get('SENTRY_DSN'),
        environment=os.environ.get('SENTRY_ENV', 'production'),
    )

# Hubspot configuration
ACCESS_TOKEN = os.environ.get('HUBSPOT_ACCESS_TOKEN')
ENABLED = os.environ.get('MARKETING_TRIGGERS_ENABLED', 'False') == 'True'

# Initialize Hubspot client
hubspot_client = HubSpot(access_token=ACCESS_TOKEN)


def list_subscription_request_v1(method, mailing_list_id, contact_id):
    """
    Uses deprecated list id
    """
    headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": f"Bearer {ACCESS_TOKEN}"
    }
    url = f"https://api.hubapi.com/contacts/v1/lists/{mailing_list_id}/{method}/"

    try:
        data = json.dumps({"vids": [int(contact_id)]})
        response = requests.post(url, data=data, headers=headers)
        return response.json()
    except Exception as e:
        error_message = f"Exception when updating contact subscription ({method}): {contact_id} to {mailing_list_id}: {e}"
        print(error_message)
        if SENTRY_ENABLED:
            sentry_sdk.capture_message(error_message)
    return {}

def list_subscription_request_v3(method, mailing_list_id, contact_id):
    """
    Uses list ILS id
    """
    headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": f"Bearer {ACCESS_TOKEN}"
    }
    url = f"https://api.hubapi.com/crm/v3/lists/{mailing_list_id}/memberships/{method}"

    try:
        response = requests.put(url, json=[int(contact_id)], headers=headers)
        return response.json()
    except Exception as e:
        error_message = f"Exception when updating contact subscription ({method}): {contact_id} to {mailing_list_id}: {e}"
        print(error_message)
        if SENTRY_ENABLED:
            sentry_sdk.capture_message(error_message)
    return {}

def create_contact(data, is_guest=False):
    if not ENABLED:
        return {"status": "disabled"}

    try:
        response = hubspot_client.crm.contacts.basic_api.create(
            simple_public_object_input_for_create=data)
        email = data.get('properties', {}).get('email')
        print(f"hubspot.create_contact: {email}[{response.id}]")
        return {"id": response.id, "status": "success"}
    except Exception as e:
        error_message = f"Exception when creating contact: {data.get('properties', {}).get('email')}: {e}"
        print(error_message)
        if SENTRY_ENABLED:
            sentry_sdk.capture_message(error_message)
        return {"status": "error", "message": str(e)}

def update_contact(contact_id, data):
    if not ENABLED:
        return {"status": "disabled"}

    try:
        hubspot_client.crm.contacts.basic_api.update(contact_id, simple_public_object_input=data)
        print(f"hubspot.update_contact: {contact_id}")
        return {"id": contact_id, "status": "success"}
    except Exception as e:
        error_message = f"Exception when updating contact: {contact_id}: {e}"
        print(error_message)
        if SENTRY_ENABLED:
            sentry_sdk.capture_message(error_message)
        return {"status": "error", "message": str(e)}

def subscribe_to_list(contact_id, mailing_list_id):
    if not ENABLED:
        return {"status": "disabled"}

    try:
        response = list_subscription_request_v1('add', mailing_list_id, contact_id)
        if (response.get('status') == 'error' and
                response.get('category') == 'OBJECT_NOT_FOUND'):
            response = list_subscription_request_v3('add', mailing_list_id, contact_id)
        print(f"hubspot.subscribe_user: {contact_id} to {mailing_list_id}")
        return {"status": "success", "response": response}
    except Exception as e:
        error_message = f"Exception when subscribing user: {contact_id} to {mailing_list_id}: {e}"
        print(error_message)
        if SENTRY_ENABLED:
            sentry_sdk.capture_message(error_message)
        return {"status": "error", "message": str(e)}

def unsubscribe_from_list(contact_id, mailing_list_id):
    if not ENABLED:
        return {"status": "disabled"}

    try:
        response = list_subscription_request_v1('remove', mailing_list_id, contact_id)
        if (response.get('status') == 'error' and
                response.get('category') == 'OBJECT_NOT_FOUND'):
            response = list_subscription_request_v3('remove', mailing_list_id, contact_id)
        print(f"hubspot.unsubscribe_user: {contact_id} from {mailing_list_id}")
        return {"status": "success", "response": response}
    except Exception as e:
        error_message = f"Exception when unsubscribing user: {contact_id} from {mailing_list_id}: {e}"
        print(error_message)
        if SENTRY_ENABLED:
            sentry_sdk.capture_message(error_message)
        return {"status": "error", "message": str(e)}

def update_subscription_preferences(email, subscription_id):
    if not ENABLED:
        return {"status": "disabled"}

    try:
        subscription_preferences = PublicUpdateSubscriptionStatusRequest(
            email_address=email,
            subscription_id=subscription_id,
            legal_basis="LEGITIMATE_INTEREST_PQL",
            legal_basis_explanation="Update email")
        response = hubspot_client.communication_preferences.status_api.subscribe(
            public_update_subscription_status_request=subscription_preferences)
        print(f"hubspot.update_contact_subscription_preferences: {subscription_id}:{email}")
        return {"status": "success"}
    except Exception as e:
        error_message = f"Exception when updating subscription preferences: {email}: {e}"
        print(error_message)
        if SENTRY_ENABLED:
            sentry_sdk.capture_message(error_message)
        return {"status": "error", "message": str(e)}

def send_callback(callback_url, callback_token, result_data):
    """
    Send callback to the specified URL with the result data
    """
    if not callback_url or not callback_token:
        return

    callback_payload = {
        "callback_token": callback_token,
        **result_data
    }

    try:
        response = requests.post(callback_url, json=callback_payload)
        print(f"Callback sent to {callback_url}: {response.status_code}")
    except Exception as e:
        error_message = f"Failed to send callback to {callback_url}: {e}"
        print(error_message)
        if SENTRY_ENABLED:
            sentry_sdk.capture_message(error_message)

def main(args):
    """
    Main function for Digital Ocean Function
    """
    action = args.get('action')
    callback_url = args.get('callback_url')
    callback_token = args.get('callback_token')

    actions_map = {
        "create_contact": create_contact,
        "update_contact": update_contact,
        "subscribe_to_list": subscribe_to_list,
        "unsubscribe_from_list": unsubscribe_from_list,
        "update_subscription_preferences": update_subscription_preferences,
    }

    if not action:
        return {"status": "error", "message": "No action provided"}
    if not actions_map.get(action):
        return {"status": "error", "message": f"Unknown action: {action}"}

    # Execute the action
    result = actions_map.get(action)(args)

    # Send callback if requested
    if callback_url and callback_token:
        send_callback(callback_url, callback_token, result)

    return result
