#!/bin/bash

# Deploy Digital Ocean Functions
cd functions

# Install doctl if not already installed
if ! command -v doctl &> /dev/null; then
    echo "Installing doctl..."
    # For Linux
    wget https://github.com/digitalocean/doctl/releases/download/v1.92.1/doctl-1.92.1-linux-amd64.tar.gz
    tar xf doctl-1.92.1-linux-amd64.tar.gz
    sudo mv doctl /usr/local/bin
    rm doctl-1.92.1-linux-amd64.tar.gz
fi

# Authenticate with Digital Ocean
doctl auth init --access-token $DIGITAL_OCEAN_ACCESS_TOKEN

# Install serverless plugin if not already installed
if ! doctl serverless status &> /dev/null; then
    echo "Installing serverless plugin..."
    doctl serverless install
fi

# Connect to namespace
doctl serverless connect --namespace $DIGITAL_OCEAN_FUNCTIONS_NAMESPACE

# Deploy functions
doctl serverless deploy .

echo "Functions deployed successfully!"
