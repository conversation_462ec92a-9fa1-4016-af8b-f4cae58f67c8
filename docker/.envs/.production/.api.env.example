# Django
DEBUG=True
SECRET_KEY=""
DOMAIN=localhost
ALLOWED_HOSTS='["localhost"]'

# Sentry
ENABLE_SENTRY=False
SENTRY_DSN=
SENTRY_ENV=development

# Backend
DJANGO_ENV=dev
DJANGO_SETTINGS_MODULE=config.settings
LANGUAGE_CODE=en-US
TIME_ZONE=Europe/Lisbon

# Admin
STAFF='{"Pedro": {"first_name": "<PERSON>", "last_name": "Gaudênc<PERSON>"}'
ADMIN_PASSWORD=

# Cache
REDIS_HOST=redis_db
REDIS_PORT=6380
CELERY_BROKER=redis://redis:6379/0
CELERY_BACKEND=redis://redis:6379/0

# CMS
CMS_URL=

# Meilisearch
MEILISEARCH_URL=
MEILISEARCH_API_TOKEN=

# Digital Ocean Functions
DIGITAL_OCEAN_FUNCTIONS_NAMESPACE=advaya
DIGITAL_OCEAN_FUNCTIONS_REGION=lon
DIGITAL_OCEAN_FUNCTIONS_AUTH_TOKEN=

# Chargebee
CHARGEBEE_API_KEY=
CHARGEBEE_SITE=
CHARGEBEE_MAGIC_KEY=
CHARGEBEE_FREE_PLAN_ID=
CHARGEBEE_COMMUNITY_MONTHLY_PLAN_ID=
CHARGEBEE_COMMUNITY_YEARLY_PLAN_ID=
CHARGEBEE_THRIVE_MONTHLY_PLAN_ID=
CHARGEBEE_THRIVE_YEARLY_PLAN_ID=

# Hubspot
HUBSPOT_ACCESS_TOKEN=
HUBSPOT_NEWSLETTER_GENERAL_LIST_ID=
HUBSPOT_NEWSLETTER_GLOBAL_USERS_LIST_ID=
HUBSPOT_FREE_MEMBERS_LIST_ID=
HUBSPOT_GIFT_GIVERS_LIST_ID=
HUBSPOT_GIFT_RECEIVERS_LIST_ID=
HUBSPOT_INTERESTS_LIST_ID=
HUBSPOT_ABANDONED_CART_YEARLY_LIST_ID=
HUBSPOT_ABANDONED_CART_QUARTERLY_LIST_ID=
HUBSPOT_ABANDONED_CART_PURCHASE_LIST_ID=
